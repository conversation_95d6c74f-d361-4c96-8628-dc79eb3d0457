const ccxt = require('ccxt');

async function checkFundingRatesSupport() {
    console.log('Проверка поддержки метода fetchFundingRates для всех бирж CCXT:\n');

    // Получаем список всех поддерживаемых бирж
    const exchanges = ccxt.exchanges;
    const supportedExchanges = [];

    for (const exchangeId of exchanges) {
        try {
            // Создаем экземпляр биржи
            const ExchangeClass = ccxt[exchangeId];
            const exchange = new ExchangeClass();

            // Проверяем поддержку метода fetchFundingRates
            const supportsFundingRates = exchange.has['fetchFundingRates'] || false;

            console.log(`${exchangeId}: ${supportsFundingRates}`);

            if (supportsFundingRates) {
                supportedExchanges.push({ id: exchangeId, exchange: exchange });
            }

        } catch (error) {
            // Если не удалось создать экземпляр биржи
            console.log(`${exchangeId}: false (ошибка создания экземпляра)`);
        }
    }

    console.log(`\nНайдено ${supportedExchanges.length} бирж с поддержкой fetchFundingRates\n`);

    // Получаем фандинг рейты с поддерживаемых бирж
    await getFundingRatesAndCalculateSpread(supportedExchanges);
}

async function getFundingRatesAndCalculateSpread(supportedExchanges) {
    console.log('Получение фандинг рейтов и расчет спредов:\n');

    const fundingData = {};

    // Получаем фандинг рейты с каждой биржи
    for (const { id, exchange } of supportedExchanges) {
        try {
            console.log(`Получение данных с ${id}...`);

            // Устанавливаем sandbox режим если доступен
            if (exchange.has['sandbox']) {
                exchange.sandbox = true;
            }

            const fundingRates = await exchange.fetchFundingRates();

            if (fundingRates && Object.keys(fundingRates).length > 0) {
                fundingData[id] = fundingRates;
                console.log(`✓ ${id}: получено ${Object.keys(fundingRates).length} фандинг рейтов`);
            } else {
                console.log(`✗ ${id}: нет данных о фандинг рейтах`);
            }

            // Небольшая задержка между запросами
            await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
            console.log(`✗ ${id}: ошибка получения данных - ${error.message}`);
        }
    }

    console.log('\n=== АНАЛИЗ СПРЕДОВ ===\n');

    // Находим общие символы между биржами
    const allSymbols = new Set();
    Object.values(fundingData).forEach(rates => {
        Object.keys(rates).forEach(symbol => allSymbols.add(symbol));
    });

    // Рассчитываем спреды для каждого символа
    for (const symbol of allSymbols) {
        const exchangesWithSymbol = [];

        // Собираем данные по символу с разных бирж
        for (const [exchangeId, rates] of Object.entries(fundingData)) {
            if (rates[symbol] && rates[symbol].fundingRate !== undefined) {
                exchangesWithSymbol.push({
                    exchange: exchangeId,
                    rate: rates[symbol].fundingRate * 100 // конвертируем в проценты
                });
            }
        }

        // Если символ есть на нескольких биржах, рассчитываем спреды
        if (exchangesWithSymbol.length >= 2) {
            console.log(`\n📊 ${symbol}:`);

            // Сортируем по фандинг рейту
            exchangesWithSymbol.sort((a, b) => a.rate - b.rate);

            // Выводим рейты по биржам
            exchangesWithSymbol.forEach(({ exchange, rate }) => {
                console.log(`  ${exchange}: ${rate.toFixed(4)}%`);
            });

            // Рассчитываем максимальный спред
            const minRate = exchangesWithSymbol[0].rate;
            const maxRate = exchangesWithSymbol[exchangesWithSymbol.length - 1].rate;
            const spread = maxRate - minRate;

            console.log(`  📈 Спред: ${spread.toFixed(4)}% (${exchangesWithSymbol[0].exchange} → ${exchangesWithSymbol[exchangesWithSymbol.length - 1].exchange})`);

            // Выделяем значительные спреды
            if (Math.abs(spread) > 0.01) {
                console.log(`  🔥 ЗНАЧИТЕЛЬНЫЙ СПРЕД!`);
            }
        }
    }
}

// Запускаем проверку
checkFundingRatesSupport().catch(console.error);
