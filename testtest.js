const ccxt = require('ccxt');

async function checkFundingRatesSupport() {
    console.log('Проверка поддержки метода fetchFundingRates для всех бирж CCXT:\n');
    
    // Получаем список всех поддерживаемых бирж
    const exchanges = ccxt.exchanges;
    
    for (const exchangeId of exchanges) {
        try {
            // Создаем экземпляр биржи
            const ExchangeClass = ccxt[exchangeId];
            const exchange = new ExchangeClass();
            
            // Проверяем поддержку метода fetchFundingRates
            const supportsFundingRates = exchange.has['fetchFundingRates'] || false;
            
            console.log(`${exchangeId}: ${supportsFundingRates}`);
            
        } catch (error) {
            // Если не удалось создать экземпляр биржи
            console.log(`${exchangeId}: false (ошибка создания экземпляра)`);
        }
    }
}

// Запускаем проверку
checkFundingRatesSupport().catch(console.error);
