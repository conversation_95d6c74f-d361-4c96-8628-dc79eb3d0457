const { bybitAPI } = require('./bybit_lib');

async function getBybitFundings() {
    try {
        const fundings = await bybitAPI.getAllFundings();
        console.log('=== ФАНДИНГИ BYBIT ===');
        console.log(fundings.slice(0, 3)); // Показываем первые 3 для примера
        console.log(`Всего фандингов: ${fundings.length}`);
        return fundings;
    } catch (error) {
        console.error('Ошибка при получении фандингов с Bybit:', error.message);
        return [];
    }
}

async function getBybitFuturesPrices() {
    try {
        const prices = await bybitAPI.getAllFuturesPrices();
        console.log('=== ЦЕНЫ ФЬЮЧЕРСОВ BYBIT ===');
        const pricesArray = Object.values(prices);
        console.log(pricesArray.slice(0, 3)); // Показываем первые 3 для примера
        console.log(`Всего фьючерсов: ${pricesArray.length}`);
        return prices;
    } catch (error) {
        console.error('Ошибка при получении цен фьючерсов с Bybit:', error.message);
        return {};
    }
}

async function getBybitCombinedData() {
    try {
        const combined = await bybitAPI.getCombinedData();
        console.log('=== КОМБИНИРОВАННЫЕ ДАННЫЕ BYBIT ===');
        console.log(combined); // Показываем первые 2 для примера
        console.log(`Всего токенов: ${combined.length}`);
        return combined;
    } catch (error) {
        console.error('Ошибка при получении комбинированных данных с Bybit:', error.message);
        return [];
    }
}



// Раскомментируйте нужную функцию для тестирования:
// getBybitFundings();
// getBybitFuturesPrices();
getBybitCombinedData();


module.exports = {
    getBybitFundings,
    getBybitFuturesPrices,
    getBybitCombinedData
};
