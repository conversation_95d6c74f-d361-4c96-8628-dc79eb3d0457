const BitmexAPI = require('./bitmex_lib');

async function main() {
    const bitmex = new BitmexAPI();

    try {
        const combinedData = await bitmex.getCombinedData();

        const withFunding = combinedData.filter(item => item.fundingRate !== undefined) //.slice(0, 5);
        console.log(withFunding.length);


    } catch (error) {
        console.error('Error:', error);
        console.error('Данные с биржи BitMex не получены, ошибка:', error);
    }
}

main();
