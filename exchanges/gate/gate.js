const ccxt = require("ccxt");

const gate = new ccxt.gate({
    enableRateLimit: true,
    'options': {
        'defaultType': 'swap'  // или 'future' для некоторых бирж
    }
});



async function getAllFundingRates() {

    const gateInfo = [];

    try {
        const fundings = await gate.fetchFundingRates()
        // console.log(fundings);ы

        for (const [token] of Object.entries(fundings)) {
            const formattedData = {
                exchange: 'gate',
                token: token,
                fundingRate: fundings[token].info.funding_rate * 100, // Convert to percentage
                nextFundingTime: fundings[token].info.funding_next_apply * 1000 // Convert to milliseconds
            };

            gateInfo.push(formattedData);
        }



    } catch (e) {
        console.error('Ошибка:', e.message);
    }

    // console.log(gateInfo);

    return gateInfo;
}

getAllFundingRates();


module.exports = {
    getAllFundingRates
};

// биржа - gate
// название токена - fundings.(обьект с токеном).symbol
// ставка фандинга - fundings.(обьект с токеном).fundingRate
// время до следующего фандинга - fundings.(обьект с токеном).info.funding_next_apply









/* 
    Фандинг - fundings.(название токена).fundingRate
    Времени до след фандинга -  fundings.(название токена).info.funding_next_apply

    Комиссии  fundings.(название токена).info.
          .taker_fee_rate
          .maker_fee_rate
    
    Макс. плечо:  fundings.info.leverage_max: '25',

*/