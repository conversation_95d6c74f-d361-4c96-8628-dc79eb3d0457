const ccxt = require('ccxt');

class BybitAPI {
    constructor() {
        this.bybit = new ccxt.bybit({
            enableRateLimit: true
        });
    }

    async getAllFundings() {
        try {
            const fundingRates = await this.bybit.fetchFundingRates();
            const result = [];

            for (const [symbol, data] of Object.entries(fundingRates)) {
                result.push({
                    exchange: 'Bybit',
                    token: symbol,
                    fundingRate: data.fundingRate * 100,
                    nextFundingTime: new Date(data.fundingTimestamp).getTime()
                });
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении фандинга с Bybit:', error.message);
            return [];
        }
    }

    async getAllFuturesPrices() {
        try {
            const futuresPrices = await this.bybit.fetchTickers();

            // Оставляем только токены с fundingRate (фьючерсы), потому что тут нет фильтра по type: "spot" в ответе как у Binance
            const filteredFutures = {};

            for (const [symbol, data] of Object.entries(futuresPrices)) {
                if (data.info && data.info.fundingRate !== undefined) {
                    filteredFutures[symbol] = {
                        symbol: symbol,
                        currentPrice: data.last,
                        markPrice: data.markPrice,
                        indexPrice: data.indexPrice,
                        fundingRate: data.info.fundingRate,
                        nextFundingTime: data.info.nextFundingTime,
                        volume24h: data.baseVolume,
                        quoteVolume: data.quoteVolume,
                        change24h: data.percentage,
                        high24h: data.high,
                        low24h: data.low
                    };
                }
            }

            return filteredFutures;
        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов с Bybit:', error.message);
            return {};
        }
    }

    async getCombinedData() {
        try {
            const fundingRates = await this.bybit.fetchFundingRates();
            const futuresPrices = await this.bybit.fetchTickers();

            // Фильтруем только фьючерсы из цен
            const filteredFutures = {};
            for (const [symbol, data] of Object.entries(futuresPrices)) {
                if (data.info && data.info.fundingRate !== undefined) {
                    filteredFutures[symbol] = data;
                }
            }

            const result = [];

            for (const [symbol, fundingData] of Object.entries(fundingRates)) {
                const futureData = filteredFutures[symbol];

                result.push({
                    exchange: 'Bybit',
                    token: symbol,
                    // Данные о фандинге
                    fundingRate: fundingData.fundingRate * 100,
                    nextFundingTime: new Date(fundingData.fundingTimestamp).getTime(),
                    // Данные о цене
                    currentPrice: futureData ? futureData.last : null,
                    markPrice: futureData ? futureData.markPrice : null,
                    indexPrice: futureData ? futureData.indexPrice : null,
                    volume24h: futureData ? futureData.baseVolume : null,
                    quoteVolume: futureData ? futureData.quoteVolume : null,
                    change24h: futureData ? futureData.percentage : null,
                    high24h: futureData ? futureData.high : null,
                    low24h: futureData ? futureData.low : null,
                    // Полная информация
                    fundingInfo: fundingData,
                    futureInfo: futureData || null
                });
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении комбинированных данных с Bybit:', error.message);
            return [];
        }
    }
}

const bybitAPI = new BybitAPI();

module.exports = {
    bybitAPI,
    BybitAPI
};
