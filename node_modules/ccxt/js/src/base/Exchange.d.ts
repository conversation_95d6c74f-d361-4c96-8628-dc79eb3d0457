import * as functions from './functions.js';
import WsClient from './ws/WsClient.js';
import { OrderBook as WsOrderBook, IndexedOrderBook, CountedOrderBook } from './ws/OrderBook.js';
import type { Market, Trade, Ticker, OHLCV, OHLCVC, Order, OrderBook, Balance, Balances, Dictionary, Transaction, DepositAddressResponse, Currency, MinMax, IndexType, Int, OrderType, OrderSide, Position, FundingRate, DepositWithdrawFeeNetwork, LedgerEntry, BorrowInterest, OpenInterest, LeverageTier, TransferEntry, FundingRateHistory, Liquidation, FundingHistory, OrderRequest, MarginMode, Tickers, Greeks, Option, OptionChain, Str, Num, MarketInterface, CurrencyInterface, BalanceAccount, MarginModes, MarketType, Leverage, Leverages, LastPrice, LastPrices, Account, Strings, MarginModification, TradingFeeInterface, Currencies, TradingFees, Conversion, CancellationRequest, IsolatedBorrowRate, IsolatedBorrowRates, CrossBorrowRates, CrossBorrowRate, Dict, FundingRates, LeverageTiers, Bool, int, DepositAddress, LongShortRatio, OrderBooks, OpenInterests, ConstructorArgs } from './types.js';
export type { Market, Trade, Fee, Ticker, OHLCV, OHLCVC, Order, OrderBook, Balance, Balances, Dictionary, Transaction, DepositAddressResponse, Currency, MinMax, IndexType, Int, Bool, OrderType, OrderSide, Position, LedgerEntry, BorrowInterest, OpenInterest, LeverageTier, TransferEntry, CrossBorrowRate, FundingRateHistory, Liquidation, FundingHistory, OrderRequest, MarginMode, Tickers, Greeks, Option, OptionChain, Str, Num, MarketInterface, CurrencyInterface, BalanceAccount, MarginModes, MarketType, Leverage, Leverages, LastPrice, LastPrices, Account, Strings, Conversion, DepositAddress, LongShortRatio } from './types.js';
import { ArrayCache, ArrayCacheByTimestamp } from './ws/Cache.js';
import { OrderBook as Ob } from './ws/OrderBook.js';
import Client from './ws/Client.js';
/**
 * @class Exchange
 */
export default class Exchange {
    options: {
        [key: string]: any;
    };
    isSandboxModeEnabled: boolean;
    throttleProp: any;
    sleep: (ms: any) => Promise<unknown>;
    api: any;
    certified: boolean;
    pro: boolean;
    countries: Str[];
    proxy: any;
    proxyUrl: string;
    proxy_url: string;
    proxyUrlCallback: any;
    proxy_url_callback: any;
    httpProxy: string;
    http_proxy: string;
    httpProxyCallback: any;
    http_proxy_callback: any;
    httpsProxy: string;
    https_proxy: string;
    httpsProxyCallback: any;
    https_proxy_callback: any;
    socksProxy: string;
    socks_proxy: string;
    socksProxyCallback: any;
    socks_proxy_callback: any;
    userAgent: {
        'User-Agent': string;
    } | false;
    user_agent: {
        'User-Agent': string;
    } | false;
    wsProxy: string;
    ws_proxy: string;
    wssProxy: string;
    wss_proxy: string;
    wsSocksProxy: string;
    ws_socks_proxy: string;
    userAgents: any;
    headers: any;
    origin: string;
    MAX_VALUE: Num;
    agent: any;
    nodeHttpModuleLoaded: boolean;
    httpAgent: any;
    httpsAgent: any;
    minFundingAddressLength: Int;
    substituteCommonCurrencyCodes: boolean;
    quoteJsonNumbers: boolean;
    number: (numberString: string) => number;
    handleContentTypeApplicationZip: boolean;
    reduceFees: boolean;
    fetchImplementation: any;
    AbortError: any;
    FetchError: any;
    validateServerSsl: boolean;
    validateClientSsl: boolean;
    timeout: Int;
    verbose: boolean;
    twofa: any;
    apiKey: string;
    secret: string;
    uid: string;
    accountId: string;
    login: string;
    password: string;
    privateKey: string;
    walletAddress: string;
    token: string;
    balance: {};
    liquidations: Dictionary<Liquidation>;
    orderbooks: Dictionary<Ob>;
    tickers: Dictionary<Ticker>;
    fundingRates: Dictionary<FundingRate>;
    bidsasks: Dictionary<Ticker>;
    orders: ArrayCache;
    triggerOrders: ArrayCache;
    trades: Dictionary<ArrayCache>;
    transactions: {};
    ohlcvs: Dictionary<Dictionary<ArrayCacheByTimestamp>>;
    myLiquidations: Dictionary<Liquidation>;
    myTrades: ArrayCache;
    positions: any;
    urls: {
        logo?: string;
        api?: string | Dictionary<string>;
        test?: string | Dictionary<string>;
        www?: string;
        doc?: string[];
        api_management?: string;
        fees?: string;
        referral?: string;
    };
    requiresWeb3: boolean;
    requiresEddsa: boolean;
    precision: {
        amount: Num;
        price: Num;
        cost?: Num;
        base?: Num;
        quote?: Num;
    };
    enableLastJsonResponse: boolean;
    enableLastHttpResponse: boolean;
    enableLastResponseHeaders: boolean;
    last_http_response: any;
    last_json_response: any;
    last_response_headers: any;
    last_request_headers: any;
    last_request_body: any;
    last_request_url: any;
    last_request_path: any;
    id: string;
    markets: Dictionary<any>;
    has: Dictionary<boolean | 'emulated'>;
    features: Dictionary<Dictionary<any>>;
    status: {
        status: Str;
        updated: Num;
        eta: Num;
        url: Str;
        info: any;
    };
    requiredCredentials: {
        apiKey: Bool;
        secret: Bool;
        uid: Bool;
        login: Bool;
        password: Bool;
        twofa: Bool;
        privateKey: Bool;
        walletAddress: Bool;
        token: Bool;
    };
    rateLimit: Num;
    tokenBucket: any;
    throttler: any;
    enableRateLimit: boolean;
    httpExceptions: any;
    limits: {
        amount?: MinMax;
        cost?: MinMax;
        leverage?: MinMax;
        price?: MinMax;
    };
    fees: {
        trading: {
            tierBased: Bool;
            percentage: Bool;
            taker: Num;
            maker: Num;
        };
        funding: {
            tierBased: Bool;
            percentage: Bool;
            withdraw: {};
            deposit: {};
        };
    };
    markets_by_id: Dictionary<any>;
    symbols: string[];
    ids: string[];
    currencies: Currencies;
    baseCurrencies: any;
    quoteCurrencies: any;
    currencies_by_id: any;
    codes: any;
    reloadingMarkets: boolean;
    marketsLoading: Promise<Dictionary<any>>;
    accounts: any;
    accountsById: any;
    commonCurrencies: Dictionary<string>;
    hostname: Str;
    precisionMode: Num;
    paddingMode: Num;
    exceptions: Dictionary<string>;
    timeframes: Dictionary<number | string>;
    version: Str;
    marketsByAltname: Dictionary<any>;
    name: Str;
    lastRestRequestTimestamp: number;
    targetAccount: any;
    stablePairs: {};
    clients: Dictionary<WsClient>;
    newUpdates: boolean;
    streaming: {};
    alias: boolean;
    deepExtend: (...xs: any) => any;
    isNode: boolean;
    keys: {
        (o: object): string[];
        (o: {}): string[];
    };
    values: (x: any[] | Dictionary<any>) => any[];
    extend: (...args: any[]) => any;
    clone: (x: any) => any;
    flatten: (x: any[], out?: any[]) => any[];
    unique: (x: any[]) => any[];
    indexBy: (x: Dictionary<any>, k: IndexType, out?: Dictionary<any>) => Dictionary<any>;
    roundTimeframe: (timeframe: string, timestamp: number, direction?: number) => number;
    sortBy: (array: any[], key: IndexType, descending?: boolean, defaultValue?: any, direction?: number) => any[];
    sortBy2: (array: any[], key1: IndexType, key2: IndexType, descending?: boolean, direction?: number) => any[];
    groupBy: (x: Dictionary<any>, k: string, out?: Dictionary<any>) => Dictionary<any>;
    aggregate: typeof functions.aggregate;
    uuid: (a?: any) => string;
    unCamelCase: (s: string) => string;
    precisionFromString: typeof functions.precisionFromString;
    capitalize: (s: string) => string;
    now: () => number;
    decimalToPrecision: (x: string, roundingMode: number, numPrecisionDigits: any, countingMode?: number, paddingMode?: number) => string;
    safeValue: (o: any, k: IndexType, $default?: any) => any;
    safeValue2: (o: any, k1: IndexType, k2: IndexType, $default?: any) => any;
    safeString: (o: any, k: IndexType, $default?: string) => string;
    safeString2: (o: any, k1: IndexType, k2: IndexType, $default?: string) => string;
    safeFloat: (o: any, k: IndexType, $default?: number) => number;
    safeFloat2: (o: any, k1: IndexType, k2: IndexType, $default?: number) => number;
    seconds: () => number;
    milliseconds: () => number;
    binaryToBase16: (data: Uint8Array) => string;
    numberToBE: (n: number, padding: number) => Uint8Array;
    base16ToBinary: (str: string) => Uint8Array;
    iso8601: (timestamp: any) => string;
    omit: (x: Dictionary<any>, ...args: any) => any;
    isJsonEncodedObject: (object: any) => boolean;
    safeInteger: (o: any, k: IndexType, $default?: number) => number;
    sum: (...xs: any[]) => any;
    omitZero: typeof functions.omitZero;
    implodeParams: (string: string, params: any[] | Dictionary<any>) => string;
    extractParams: (string: string) => string[];
    json: (data: any, params?: any) => string;
    vwap: typeof functions.vwap;
    merge: (target: Dictionary<any>, ...args: any) => Dictionary<any>;
    binaryConcat: typeof import("../static_dependencies/noble-curves/abstract/utils.js").concatBytes;
    hash: (request: import("../static_dependencies/noble-hashes/utils.js").Input, hash: {
        (message: import("../static_dependencies/noble-hashes/utils.js").Input): Uint8Array;
        outputLen: number;
        blockLen: number;
        create(): import("../static_dependencies/noble-hashes/utils.js").Hash<import("../static_dependencies/noble-hashes/utils.js").Hash<any>>;
    }, digest?: "binary" | "hex" | "base64") => any;
    arrayConcat: (a: any[], b: any[]) => any[];
    encode: (str: string) => Uint8Array;
    urlencode: (object: object) => string;
    hmac: (request: import("../static_dependencies/noble-hashes/utils.js").Input, secret: import("../static_dependencies/noble-hashes/utils.js").Input, hash: {
        (message: import("../static_dependencies/noble-hashes/utils.js").Input): Uint8Array;
        outputLen: number;
        blockLen: number;
        create(): import("../static_dependencies/noble-hashes/utils.js").Hash<import("../static_dependencies/noble-hashes/utils.js").Hash<any>>;
    }, digest?: "binary" | "hex" | "base64") => any;
    numberToString: typeof functions.numberToString;
    parseTimeframe: (timeframe: string) => number;
    safeInteger2: (o: any, k1: IndexType, k2: IndexType, $default?: number) => number;
    safeStringLower: (o: any, k: IndexType, $default?: string) => string;
    parse8601: (x: any) => number;
    yyyymmdd: (timestamp: any, infix?: string) => string;
    safeStringUpper: (o: any, k: IndexType, $default?: string) => string;
    safeTimestamp: (o: any, k: IndexType, $default?: number) => number;
    binaryConcatArray: (arr: any[]) => Uint8Array;
    uuidv1: () => string;
    numberToLE: (n: number, padding: number) => Uint8Array;
    ymdhms: (timestamp: any, infix?: string) => string;
    yymmdd: (timestamp: any, infix?: string) => string;
    stringToBase64: (string: string) => string;
    decode: (data: Uint8Array) => string;
    uuid22: (a?: any) => string;
    safeIntegerProduct2: (o: any, k1: IndexType, k2: IndexType, $factor: number, $default?: number) => number;
    safeIntegerProduct: (o: any, k: IndexType, $factor: number, $default?: number) => number;
    binaryToBase58: (data: Uint8Array) => string;
    base58ToBinary: (str: string) => Uint8Array;
    base64ToBinary: (str: string) => Uint8Array;
    safeTimestamp2: (o: any, k1: IndexType, k2: IndexType, $default?: number) => number;
    rawencode: (object: object) => string;
    keysort: (x: Dictionary<any>, out?: Dictionary<any>) => Dictionary<any>;
    inArray: (needle: any, haystack: any[]) => boolean;
    safeStringLower2: (o: any, k1: IndexType, k2: IndexType, $default?: string) => string;
    safeStringUpper2: (o: any, k1: IndexType, k2: IndexType, $default?: string) => string;
    isEmpty: (object: any[] | Dictionary<any>) => boolean;
    ordered: (x: any[] | Dictionary<any>) => any[] | Dictionary<any>;
    filterBy: (x: Dictionary<any>, k: string, value?: any, out?: Dictionary<any>[]) => Dictionary<any>[];
    uuid16: (a?: any) => string;
    urlencodeWithArrayRepeat: (object: object) => string;
    microseconds: () => number;
    binaryToBase64: (data: Uint8Array) => string;
    strip: (s: string) => string;
    toArray: (object: any[] | Dictionary<any>) => any[];
    safeFloatN: (o: any, k: IndexType[], $default?: number) => number;
    safeIntegerN: (o: any, k: IndexType[], $default?: number) => number;
    safeIntegerProductN: (o: any, k: IndexType[], $factor: number, $default?: number) => number;
    safeTimestampN: (o: any, k: IndexType[], $default?: number) => number;
    safeValueN: (o: any, k: IndexType[], $default?: any) => any;
    safeStringN: (o: any, k: IndexType[], $default?: string) => string;
    safeStringLowerN: (o: any, k: IndexType[], $default?: string) => string;
    safeStringUpperN: (o: any, k: IndexType[], $default?: string) => string;
    urlencodeNested: (object: object) => string;
    parseDate: (x: any) => number;
    ymd: (timestamp: any, infix: any, fullYear?: boolean) => string;
    base64ToString: (string: string) => string;
    crc32: typeof functions.crc32;
    packb: typeof functions.packb;
    urlencodeBase64: (payload: string | Uint8Array) => string;
    constructor(userConfig?: ConstructorArgs);
    encodeURIComponent(...args: any[]): string;
    checkRequiredVersion(requiredVersion: any, error?: boolean): boolean;
    throttle(cost?: any): any;
    initThrottler(): void;
    defineRestApiEndpoint(methodName: any, uppercaseMethod: any, lowercaseMethod: any, camelcaseMethod: any, path: any, paths: any, config?: {}): void;
    defineRestApi(api: any, methodName: any, paths?: any[]): void;
    log(...args: any[]): void;
    httpProxyAgentModule: any;
    httpsProxyAgentModule: any;
    socksProxyAgentModule: any;
    socksProxyAgentModuleChecked: boolean;
    proxyDictionaries: any;
    proxiesModulesLoading: Promise<any>;
    loadProxyModules(): Promise<any>;
    setProxyAgents(httpProxy: any, httpsProxy: any, socksProxy: any): any;
    loadHttpProxyAgent(): Promise<any>;
    getHttpAgentIfNeeded(url: any): any;
    fetch(url: any, method?: string, headers?: any, body?: any): Promise<any>;
    parseJson(jsonString: any): any;
    getResponseHeaders(response: any): {};
    handleRestResponse(response: any, url: any, method?: string, requestHeaders?: any, requestBody?: any): any;
    onRestResponse(statusCode: any, statusText: any, url: any, method: any, responseHeaders: any, responseBody: any, requestHeaders: any, requestBody: any): any;
    onJsonResponse(responseBody: any): any;
    loadMarketsHelper(reload?: boolean, params?: {}): Promise<Dictionary<any>>;
    loadMarkets(reload?: boolean, params?: {}): Promise<Dictionary<Market>>;
    fetchCurrencies(params?: {}): Promise<Currencies>;
    fetchCurrenciesWs(params?: {}): Promise<unknown>;
    fetchMarkets(params?: {}): Promise<Market[]>;
    fetchMarketsWs(params?: {}): Promise<Market[]>;
    checkRequiredDependencies(): void;
    parseNumber(value: any, d?: Num): number;
    checkOrderArguments(market: any, type: any, side: any, amount: any, price: any, params: any): void;
    handleHttpStatusCode(code: any, reason: any, url: any, method: any, body: any): void;
    remove0xPrefix(hexData: any): any;
    spawn(method: any, ...args: any[]): import("./ws/Future.js").FutureInterface;
    delay(timeout: any, method: any, ...args: any[]): void;
    orderBook(snapshot?: {}, depth?: number): WsOrderBook;
    indexedOrderBook(snapshot?: {}, depth?: number): IndexedOrderBook;
    countedOrderBook(snapshot?: {}, depth?: number): CountedOrderBook;
    handleMessage(client: any, message: any): void;
    ping(client: Client): any;
    client(url: string): WsClient;
    watchMultiple(url: string, messageHashes: string[], message?: any, subscribeHashes?: any, subscription?: any): import("./ws/Future.js").FutureInterface;
    watch(url: string, messageHash: string, message?: any, subscribeHash?: any, subscription?: any): any;
    onConnected(client: any, message?: any): void;
    onError(client: any, error: any): void;
    onClose(client: any, error: any): void;
    close(): Promise<void>;
    loadOrderBook(client: any, messageHash: string, symbol: string, limit?: Int, params?: {}): Promise<void>;
    convertToBigInt(value: string): bigint;
    stringToCharsArray(value: string): string[];
    valueIsDefined(value: any): boolean;
    arraySlice(array: any, first: any, second?: any): any;
    getProperty(obj: any, property: any, defaultValue?: any): any;
    setProperty(obj: any, property: any, defaultValue?: any): void;
    axolotl(payload: any, hexKey: any, ed25519: any): string;
    fixStringifiedJsonMembers(content: string): string;
    ethAbiEncode(types: any, args: any): Uint8Array;
    ethEncodeStructuredData(domain: any, messageTypes: any, messageData: any): Uint8Array;
    retrieveStarkAccount(signature: any, accountClassHash: any, accountProxyClassHash: any): {
        privateKey: string;
        publicKey: string;
        address: string;
    };
    starknetEncodeStructuredData(domain: any, messageTypes: any, messageData: any, address: any): string;
    starknetSign(hash: any, pri: any): string;
    intToBase16(elem: any): string;
    extendExchangeOptions(newOptions: Dict): void;
    createSafeDictionary(): {};
    convertToSafeDictionary(dict: any): any;
    randomBytes(length: number): string;
    randNumber(size: number): number;
    binaryLength(binary: Uint8Array): number;
    describe(): any;
    safeBoolN(dictionaryOrList: any, keys: IndexType[], defaultValue?: boolean): boolean | undefined;
    safeBool2(dictionary: any, key1: IndexType, key2: IndexType, defaultValue?: boolean): boolean | undefined;
    safeBool(dictionary: any, key: IndexType, defaultValue?: boolean): boolean | undefined;
    safeDictN(dictionaryOrList: any, keys: IndexType[], defaultValue?: Dictionary<any>): Dictionary<any> | undefined;
    safeDict(dictionary: any, key: IndexType, defaultValue?: Dictionary<any>): Dictionary<any> | undefined;
    safeDict2(dictionary: any, key1: IndexType, key2: string, defaultValue?: Dictionary<any>): Dictionary<any> | undefined;
    safeListN(dictionaryOrList: any, keys: IndexType[], defaultValue?: any[]): any[] | undefined;
    safeList2(dictionaryOrList: any, key1: IndexType, key2: string, defaultValue?: any[]): any[] | undefined;
    safeList(dictionaryOrList: any, key: IndexType, defaultValue?: any[]): any[] | undefined;
    handleDeltas(orderbook: any, deltas: any): void;
    handleDelta(bookside: any, delta: any): void;
    handleDeltasWithKeys(bookSide: any, deltas: any, priceKey?: IndexType, amountKey?: IndexType, countOrIdKey?: IndexType): void;
    getCacheIndex(orderbook: any, deltas: any): number;
    findTimeframe(timeframe: any, timeframes?: any): string;
    checkProxyUrlSettings(url?: Str, method?: Str, headers?: any, body?: any): any;
    checkProxySettings(url?: Str, method?: Str, headers?: any, body?: any): any[];
    checkWsProxySettings(): any[];
    checkConflictingProxies(proxyAgentSet: any, proxyUrlSet: any): void;
    checkAddress(address?: Str): Str;
    findMessageHashes(client: any, element: string): string[];
    filterByLimit(array: object[], limit?: Int, key?: IndexType, fromStart?: boolean): any;
    filterBySinceLimit(array: object[], since?: Int, limit?: Int, key?: IndexType, tail?: boolean): any;
    filterByValueSinceLimit(array: object[], field: IndexType, value?: any, since?: Int, limit?: Int, key?: string, tail?: boolean): any;
    /**
     * @method
     * @name Exchange#setSandboxMode
     * @description set the sandbox mode for the exchange
     * @param {boolean} enabled true to enable sandbox mode, false to disable it
     */
    setSandboxMode(enabled: boolean): void;
    sign(path: any, api?: any, method?: string, params?: {}, headers?: any, body?: any): {};
    fetchAccounts(params?: {}): Promise<Account[]>;
    fetchTrades(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    fetchTradesWs(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    watchLiquidations(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Liquidation[]>;
    watchLiquidationsForSymbols(symbols: string[], since?: Int, limit?: Int, params?: {}): Promise<Liquidation[]>;
    watchMyLiquidations(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Liquidation[]>;
    watchMyLiquidationsForSymbols(symbols: string[], since?: Int, limit?: Int, params?: {}): Promise<Liquidation[]>;
    watchTrades(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    unWatchTrades(symbol: string, params?: {}): Promise<any>;
    watchTradesForSymbols(symbols: string[], since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    unWatchTradesForSymbols(symbols: string[], params?: {}): Promise<any>;
    watchMyTradesForSymbols(symbols: string[], since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    watchOrdersForSymbols(symbols: string[], since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    watchOHLCVForSymbols(symbolsAndTimeframes: string[][], since?: Int, limit?: Int, params?: {}): Promise<Dictionary<Dictionary<OHLCV[]>>>;
    unWatchOHLCVForSymbols(symbolsAndTimeframes: string[][], params?: {}): Promise<any>;
    watchOrderBookForSymbols(symbols: string[], limit?: Int, params?: {}): Promise<OrderBook>;
    unWatchOrderBookForSymbols(symbols: string[], params?: {}): Promise<any>;
    fetchDepositAddresses(codes?: Strings, params?: {}): Promise<DepositAddress[]>;
    fetchOrderBook(symbol: string, limit?: Int, params?: {}): Promise<OrderBook>;
    fetchOrderBookWs(symbol: string, limit?: Int, params?: {}): Promise<OrderBook>;
    fetchMarginMode(symbol: string, params?: {}): Promise<MarginMode>;
    fetchMarginModes(symbols?: Strings, params?: {}): Promise<MarginModes>;
    fetchRestOrderBookSafe(symbol: any, limit?: any, params?: {}): Promise<OrderBook>;
    watchOrderBook(symbol: string, limit?: Int, params?: {}): Promise<OrderBook>;
    unWatchOrderBook(symbol: string, params?: {}): Promise<any>;
    fetchTime(params?: {}): Promise<Int>;
    fetchTradingLimits(symbols?: Strings, params?: {}): Promise<{}>;
    parseCurrency(rawCurrency: Dict): Currency;
    parseCurrencies(rawCurrencies: any): Currencies;
    parseMarket(market: Dict): Market;
    parseMarkets(markets: any): Market[];
    parseTicker(ticker: Dict, market?: Market): Ticker;
    parseDepositAddress(depositAddress: any, currency?: Currency): DepositAddress;
    parseTrade(trade: Dict, market?: Market): Trade;
    parseTransaction(transaction: Dict, currency?: Currency): Transaction;
    parseTransfer(transfer: Dict, currency?: Currency): TransferEntry;
    parseAccount(account: Dict): Account;
    parseLedgerEntry(item: Dict, currency?: Currency): LedgerEntry;
    parseOrder(order: Dict, market?: Market): Order;
    fetchCrossBorrowRates(params?: {}): Promise<CrossBorrowRates>;
    fetchIsolatedBorrowRates(params?: {}): Promise<IsolatedBorrowRates>;
    parseMarketLeverageTiers(info: any, market?: Market): LeverageTier[];
    fetchLeverageTiers(symbols?: Strings, params?: {}): Promise<LeverageTiers>;
    parsePosition(position: Dict, market?: Market): Position;
    parseFundingRateHistory(info: any, market?: Market): FundingRateHistory;
    parseBorrowInterest(info: Dict, market?: Market): BorrowInterest;
    parseIsolatedBorrowRate(info: Dict, market?: Market): IsolatedBorrowRate;
    parseWsTrade(trade: Dict, market?: Market): Trade;
    parseWsOrder(order: Dict, market?: Market): Order;
    parseWsOrderTrade(trade: Dict, market?: Market): Trade;
    parseWsOHLCV(ohlcv: any, market?: Market): OHLCV;
    fetchFundingRates(symbols?: Strings, params?: {}): Promise<FundingRates>;
    fetchFundingIntervals(symbols?: Strings, params?: {}): Promise<FundingRates>;
    watchFundingRate(symbol: string, params?: {}): Promise<FundingRate>;
    watchFundingRates(symbols: string[], params?: {}): Promise<FundingRates>;
    watchFundingRatesForSymbols(symbols: string[], params?: {}): Promise<{}>;
    transfer(code: string, amount: number, fromAccount: string, toAccount: string, params?: {}): Promise<TransferEntry>;
    withdraw(code: string, amount: number, address: string, tag?: any, params?: {}): Promise<Transaction>;
    createDepositAddress(code: string, params?: {}): Promise<DepositAddressResponse>;
    setLeverage(leverage: Int, symbol?: Str, params?: {}): Promise<{}>;
    fetchLeverage(symbol: string, params?: {}): Promise<Leverage>;
    fetchLeverages(symbols?: Strings, params?: {}): Promise<Leverages>;
    setPositionMode(hedged: boolean, symbol?: Str, params?: {}): Promise<{}>;
    addMargin(symbol: string, amount: number, params?: {}): Promise<MarginModification>;
    reduceMargin(symbol: string, amount: number, params?: {}): Promise<MarginModification>;
    setMargin(symbol: string, amount: number, params?: {}): Promise<{}>;
    fetchLongShortRatio(symbol: string, timeframe?: Str, params?: {}): Promise<LongShortRatio>;
    fetchLongShortRatioHistory(symbol?: Str, timeframe?: Str, since?: Int, limit?: Int, params?: {}): Promise<LongShortRatio[]>;
    fetchMarginAdjustmentHistory(symbol?: Str, type?: Str, since?: Num, limit?: Num, params?: {}): Promise<MarginModification[]>;
    setMarginMode(marginMode: string, symbol?: Str, params?: {}): Promise<{}>;
    fetchDepositAddressesByNetwork(code: string, params?: {}): Promise<DepositAddress[]>;
    fetchOpenInterestHistory(symbol: string, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OpenInterest[]>;
    fetchOpenInterest(symbol: string, params?: {}): Promise<OpenInterest>;
    fetchOpenInterests(symbols?: Strings, params?: {}): Promise<OpenInterests>;
    signIn(params?: {}): Promise<{}>;
    fetchPaymentMethods(params?: {}): Promise<{}>;
    parseToInt(number: any): number;
    parseToNumeric(number: any): number;
    isRoundNumber(value: number): boolean;
    safeNumberOmitZero(obj: object, key: IndexType, defaultValue?: Num): Num;
    safeIntegerOmitZero(obj: object, key: IndexType, defaultValue?: Int): Int;
    afterConstruct(): void;
    initRestRateLimiter(): void;
    featuresGenerator(): void;
    featuresMapper(initialFeatures: any, marketType: Str, subType?: Str): any;
    orderbookChecksumMessage(symbol: Str): string;
    createNetworksByIdObject(): void;
    getDefaultOptions(): {
        defaultNetworkCodeReplacements: {
            ETH: {
                ERC20: string;
            };
            TRX: {
                TRC20: string;
            };
            CRO: {
                CRC20: string;
            };
            BRC20: {
                BRC20: string;
            };
        };
    };
    safeLedgerEntry(entry: object, currency?: Currency): {
        id: string;
        timestamp: number;
        datetime: string;
        direction: string;
        account: string;
        referenceId: string;
        referenceAccount: string;
        type: string;
        currency: string;
        amount: number;
        before: number;
        after: number;
        status: string;
        fee: any;
        info: Dictionary<any>;
    };
    safeCurrencyStructure(currency: object): CurrencyInterface;
    safeMarketStructure(market?: Dict): MarketInterface;
    setMarkets(markets: any, currencies?: any): Dictionary<any>;
    getDescribeForExtendedWsExchange(currentRestInstance: any, parentRestInstance: any, wsBaseDescribe: Dictionary<any>): any;
    safeBalance(balance: Dict): Balances;
    safeOrder(order: Dict, market?: Market): Order;
    parseOrders(orders: object, market?: Market, since?: Int, limit?: Int, params?: {}): Order[];
    calculateFee(symbol: string, type: string, side: string, amount: number, price: number, takerOrMaker?: string, params?: {}): {
        type: string;
        currency: any;
        rate: number;
        cost: number;
    };
    safeLiquidation(liquidation: Dict, market?: Market): Liquidation;
    safeTrade(trade: Dict, market?: Market): Trade;
    parsedFeeAndFees(container: any): Dictionary<any>[];
    parseFeeNumeric(fee: any): any;
    findNearestCeiling(arr: number[], providedValue: number): number;
    invertFlatStringDictionary(dict: any): {};
    reduceFeesByCurrency(fees: any): any[];
    safeTicker(ticker: Dict, market?: Market): Ticker;
    fetchBorrowRate(code: string, amount: number, params?: {}): Promise<{}>;
    repayCrossMargin(code: string, amount: number, params?: {}): Promise<{}>;
    repayIsolatedMargin(symbol: string, code: string, amount: number, params?: {}): Promise<{}>;
    borrowCrossMargin(code: string, amount: number, params?: {}): Promise<{}>;
    borrowIsolatedMargin(symbol: string, code: string, amount: number, params?: {}): Promise<{}>;
    borrowMargin(code: string, amount: number, symbol?: Str, params?: {}): Promise<{}>;
    repayMargin(code: string, amount: number, symbol?: Str, params?: {}): Promise<{}>;
    fetchOHLCV(symbol: string, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OHLCV[]>;
    fetchOHLCVWs(symbol: string, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OHLCV[]>;
    watchOHLCV(symbol: string, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OHLCV[]>;
    convertTradingViewToOHLCV(ohlcvs: number[][], timestamp?: string, open?: string, high?: string, low?: string, close?: string, volume?: string, ms?: boolean): any[];
    convertOHLCVToTradingView(ohlcvs: number[][], timestamp?: string, open?: string, high?: string, low?: string, close?: string, volume?: string, ms?: boolean): {};
    fetchWebEndpoint(method: any, endpointMethod: any, returnAsJson: any, startRegex?: any, endRegex?: any): Promise<any>;
    marketIds(symbols?: Strings): any[];
    currencyIds(codes?: Strings): any[];
    marketsForSymbols(symbols?: Strings): any[];
    marketSymbols(symbols?: Strings, type?: Str, allowEmpty?: boolean, sameTypeOnly?: boolean, sameSubTypeOnly?: boolean): any[];
    marketCodes(codes?: Strings): any[];
    parseBidsAsks(bidasks: any, priceKey?: IndexType, amountKey?: IndexType, countOrIdKey?: IndexType): any[];
    fetchL2OrderBook(symbol: string, limit?: Int, params?: {}): Promise<any>;
    filterBySymbol(objects: any, symbol?: Str): any;
    parseOHLCV(ohlcv: any, market?: Market): OHLCV;
    networkCodeToId(networkCode: string, currencyCode?: Str): string;
    networkIdToCode(networkId?: Str, currencyCode?: Str): string;
    handleNetworkCodeAndParams(params: any): any[];
    defaultNetworkCode(currencyCode: string): any;
    selectNetworkCodeFromUnifiedNetworks(currencyCode: any, networkCode: any, indexedNetworkEntries: any): any;
    selectNetworkIdFromRawNetworks(currencyCode: any, networkCode: any, indexedNetworkEntries: any): any;
    selectNetworkKeyFromNetworks(currencyCode: any, networkCode: any, indexedNetworkEntries: any, isIndexedByUnifiedNetworkCode?: boolean): any;
    safeNumber2(dictionary: object, key1: IndexType, key2: IndexType, d?: any): number;
    parseOrderBook(orderbook: object, symbol: string, timestamp?: Int, bidsKey?: string, asksKey?: string, priceKey?: IndexType, amountKey?: IndexType, countOrIdKey?: IndexType): OrderBook;
    parseOHLCVs(ohlcvs: object[], market?: any, timeframe?: string, since?: Int, limit?: Int, tail?: Bool): OHLCV[];
    parseLeverageTiers(response: any, symbols?: string[], marketIdKey?: any): LeverageTiers;
    loadTradingLimits(symbols?: Strings, reload?: boolean, params?: {}): Promise<Dictionary<any>>;
    safePosition(position: Dict): Position;
    parsePositions(positions: any[], symbols?: string[], params?: {}): Position[];
    parseAccounts(accounts: any[], params?: {}): Account[];
    parseTrades(trades: any[], market?: Market, since?: Int, limit?: Int, params?: {}): Trade[];
    parseTransactions(transactions: any[], currency?: Currency, since?: Int, limit?: Int, params?: {}): Transaction[];
    parseTransfers(transfers: any[], currency?: Currency, since?: Int, limit?: Int, params?: {}): TransferEntry[];
    parseLedger(data: any, currency?: Currency, since?: Int, limit?: Int, params?: {}): LedgerEntry[];
    nonce(): number;
    setHeaders(headers: any): any;
    currencyId(code: string): string;
    marketId(symbol: string): string;
    symbol(symbol: string): string;
    handleParamString(params: object, paramName: string, defaultValue?: Str): [string, object];
    handleParamString2(params: object, paramName1: string, paramName2: string, defaultValue?: Str): [string, object];
    handleParamInteger(params: object, paramName: string, defaultValue?: Int): [Int, object];
    handleParamInteger2(params: object, paramName1: string, paramName2: string, defaultValue?: Int): [Int, object];
    handleParamBool(params: object, paramName: string, defaultValue?: Bool): [Bool, object];
    handleParamBool2(params: object, paramName1: string, paramName2: string, defaultValue?: Bool): [Bool, object];
    /**
     * @param {object} params - extra parameters
     * @param {object} request - existing dictionary of request
     * @param {string} exchangeSpecificKey - the key for chain id to be set in request
     * @param {object} currencyCode - (optional) existing dictionary of request
     * @param {boolean} isRequired - (optional) whether that param is required to be present
     * @returns {object[]} - returns [request, params] where request is the modified request object and params is the modified params object
     */
    handleRequestNetwork(params: Dict, request: Dict, exchangeSpecificKey: string, currencyCode?: Str, isRequired?: boolean): Dict[];
    resolvePath(path: any, params: any): any[];
    getListFromObjectValues(objects: any, key: IndexType): any[];
    getSymbolsForMarketType(marketType?: Str, subType?: Str, symbolWithActiveStatus?: boolean, symbolWithUnknownStatus?: boolean): any[];
    filterByArray(objects: any, key: IndexType, values?: any, indexed?: boolean): any;
    fetch2(path: any, api?: any, method?: string, params?: {}, headers?: any, body?: any, config?: {}): Promise<any>;
    request(path: any, api?: any, method?: string, params?: {}, headers?: any, body?: any, config?: {}): Promise<any>;
    loadAccounts(reload?: boolean, params?: {}): Promise<any>;
    buildOHLCVC(trades: Trade[], timeframe?: string, since?: number, limit?: number): OHLCVC[];
    parseTradingViewOHLCV(ohlcvs: any, market?: any, timeframe?: string, since?: Int, limit?: Int): OHLCV[];
    editLimitBuyOrder(id: string, symbol: string, amount: number, price?: Num, params?: {}): Promise<Order>;
    editLimitSellOrder(id: string, symbol: string, amount: number, price?: Num, params?: {}): Promise<Order>;
    editLimitOrder(id: string, symbol: string, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    editOrder(id: string, symbol: string, type: OrderType, side: OrderSide, amount?: Num, price?: Num, params?: {}): Promise<Order>;
    editOrderWs(id: string, symbol: string, type: OrderType, side: OrderSide, amount?: Num, price?: Num, params?: {}): Promise<Order>;
    fetchPosition(symbol: string, params?: {}): Promise<Position>;
    fetchPositionWs(symbol: string, params?: {}): Promise<Position[]>;
    watchPosition(symbol?: Str, params?: {}): Promise<Position>;
    watchPositions(symbols?: Strings, since?: Int, limit?: Int, params?: {}): Promise<Position[]>;
    watchPositionForSymbols(symbols?: Strings, since?: Int, limit?: Int, params?: {}): Promise<Position[]>;
    fetchPositionsForSymbol(symbol: string, params?: {}): Promise<Position[]>;
    fetchPositionsForSymbolWs(symbol: string, params?: {}): Promise<Position[]>;
    fetchPositions(symbols?: Strings, params?: {}): Promise<Position[]>;
    fetchPositionsWs(symbols?: Strings, params?: {}): Promise<Position[]>;
    fetchPositionsRisk(symbols?: Strings, params?: {}): Promise<Position[]>;
    fetchBidsAsks(symbols?: Strings, params?: {}): Promise<Tickers>;
    fetchBorrowInterest(code?: Str, symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<BorrowInterest[]>;
    fetchLedger(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<LedgerEntry[]>;
    fetchLedgerEntry(id: string, code?: Str, params?: {}): Promise<LedgerEntry>;
    parseBidAsk(bidask: any, priceKey?: IndexType, amountKey?: IndexType, countOrIdKey?: IndexType): number[];
    safeCurrency(currencyId: Str, currency?: Currency): CurrencyInterface;
    safeMarket(marketId?: Str, market?: Market, delimiter?: Str, marketType?: Str): MarketInterface;
    checkRequiredCredentials(error?: boolean): boolean;
    oath(): string;
    fetchBalance(params?: {}): Promise<Balances>;
    fetchBalanceWs(params?: {}): Promise<Balances>;
    parseBalance(response: any): Balances;
    watchBalance(params?: {}): Promise<Balances>;
    fetchPartialBalance(part: any, params?: {}): Promise<Balance>;
    fetchFreeBalance(params?: {}): Promise<Balance>;
    fetchUsedBalance(params?: {}): Promise<Balance>;
    fetchTotalBalance(params?: {}): Promise<Balance>;
    fetchStatus(params?: {}): Promise<any>;
    fetchTransactionFee(code: string, params?: {}): Promise<{}>;
    fetchTransactionFees(codes?: Strings, params?: {}): Promise<{}>;
    fetchDepositWithdrawFees(codes?: Strings, params?: {}): Promise<Dictionary<DepositWithdrawFeeNetwork>>;
    fetchDepositWithdrawFee(code: string, params?: {}): Promise<DepositWithdrawFeeNetwork>;
    getSupportedMapping(key: any, mapping?: {}): any;
    fetchCrossBorrowRate(code: string, params?: {}): Promise<CrossBorrowRate>;
    fetchIsolatedBorrowRate(symbol: string, params?: {}): Promise<IsolatedBorrowRate>;
    handleOptionAndParams(params: object, methodName: string, optionName: string, defaultValue?: any): any[];
    handleOptionAndParams2(params: object, methodName1: string, optionName1: string, optionName2: string, defaultValue?: any): any[];
    handleOption(methodName: string, optionName: string, defaultValue?: any): any;
    handleMarketTypeAndParams(methodName: string, market?: Market, params?: {}, defaultValue?: any): any;
    handleSubTypeAndParams(methodName: string, market?: any, params?: {}, defaultValue?: any): any[];
    handleMarginModeAndParams(methodName: string, params?: {}, defaultValue?: any): any[];
    throwExactlyMatchedException(exact: any, string: any, message: any): void;
    throwBroadlyMatchedException(broad: any, string: any, message: any): void;
    findBroadlyMatchedKey(broad: any, string: any): string;
    handleErrors(statusCode: int, statusText: string, url: string, method: string, responseHeaders: Dict, responseBody: string, response: any, requestHeaders: any, requestBody: any): any;
    calculateRateLimiterCost(api: any, method: any, path: any, params: any, config?: {}): any;
    fetchTicker(symbol: string, params?: {}): Promise<Ticker>;
    fetchMarkPrice(symbol: string, params?: {}): Promise<Ticker>;
    fetchTickerWs(symbol: string, params?: {}): Promise<Ticker>;
    watchTicker(symbol: string, params?: {}): Promise<Ticker>;
    fetchTickers(symbols?: Strings, params?: {}): Promise<Tickers>;
    fetchMarkPrices(symbols?: Strings, params?: {}): Promise<Tickers>;
    fetchTickersWs(symbols?: Strings, params?: {}): Promise<Tickers>;
    fetchOrderBooks(symbols?: Strings, limit?: Int, params?: {}): Promise<OrderBooks>;
    watchBidsAsks(symbols?: Strings, params?: {}): Promise<Tickers>;
    watchTickers(symbols?: Strings, params?: {}): Promise<Tickers>;
    unWatchTickers(symbols?: Strings, params?: {}): Promise<any>;
    fetchOrder(id: string, symbol?: Str, params?: {}): Promise<Order>;
    fetchOrderWs(id: string, symbol?: Str, params?: {}): Promise<Order>;
    fetchOrderStatus(id: string, symbol?: Str, params?: {}): Promise<string>;
    fetchUnifiedOrder(order: any, params?: {}): Promise<Order>;
    createOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createConvertTrade(id: string, fromCode: string, toCode: string, amount?: Num, params?: {}): Promise<Conversion>;
    fetchConvertTrade(id: string, code?: Str, params?: {}): Promise<Conversion>;
    fetchConvertTradeHistory(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<Conversion[]>;
    fetchPositionMode(symbol?: Str, params?: {}): Promise<{}>;
    createTrailingAmountOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, trailingAmount?: any, trailingTriggerPrice?: any, params?: {}): Promise<Order>;
    createTrailingAmountOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, trailingAmount?: any, trailingTriggerPrice?: any, params?: {}): Promise<Order>;
    createTrailingPercentOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, trailingPercent?: any, trailingTriggerPrice?: any, params?: {}): Promise<Order>;
    createTrailingPercentOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, trailingPercent?: any, trailingTriggerPrice?: any, params?: {}): Promise<Order>;
    createMarketOrderWithCost(symbol: string, side: OrderSide, cost: number, params?: {}): Promise<Order>;
    createMarketBuyOrderWithCost(symbol: string, cost: number, params?: {}): Promise<Order>;
    createMarketSellOrderWithCost(symbol: string, cost: number, params?: {}): Promise<Order>;
    createMarketOrderWithCostWs(symbol: string, side: OrderSide, cost: number, params?: {}): Promise<Order>;
    createTriggerOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, triggerPrice?: Num, params?: {}): Promise<Order>;
    createTriggerOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, triggerPrice?: Num, params?: {}): Promise<Order>;
    createStopLossOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, stopLossPrice?: Num, params?: {}): Promise<Order>;
    createStopLossOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, stopLossPrice?: Num, params?: {}): Promise<Order>;
    createTakeProfitOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, takeProfitPrice?: Num, params?: {}): Promise<Order>;
    createTakeProfitOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, takeProfitPrice?: Num, params?: {}): Promise<Order>;
    createOrderWithTakeProfitAndStopLoss(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, takeProfit?: Num, stopLoss?: Num, params?: {}): Promise<Order>;
    setTakeProfitAndStopLossParams(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, takeProfit?: Num, stopLoss?: Num, params?: {}): {};
    createOrderWithTakeProfitAndStopLossWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, takeProfit?: Num, stopLoss?: Num, params?: {}): Promise<Order>;
    createOrders(orders: OrderRequest[], params?: {}): Promise<Order[]>;
    editOrders(orders: OrderRequest[], params?: {}): Promise<Order[]>;
    createOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    cancelOrder(id: string, symbol?: Str, params?: {}): Promise<{}>;
    cancelOrderWs(id: string, symbol?: Str, params?: {}): Promise<{}>;
    cancelOrdersWs(ids: string[], symbol?: Str, params?: {}): Promise<{}>;
    cancelAllOrders(symbol?: Str, params?: {}): Promise<{}>;
    cancelAllOrdersAfter(timeout: Int, params?: {}): Promise<{}>;
    cancelOrdersForSymbols(orders: CancellationRequest[], params?: {}): Promise<{}>;
    cancelAllOrdersWs(symbol?: Str, params?: {}): Promise<{}>;
    cancelUnifiedOrder(order: any, params?: {}): Promise<{}>;
    fetchOrders(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchOrdersWs(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchOrderTrades(id: string, symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    watchOrders(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchOpenOrders(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchOpenOrdersWs(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchClosedOrders(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchCanceledAndClosedOrders(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchClosedOrdersWs(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Order[]>;
    fetchMyTrades(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    fetchMyLiquidations(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Liquidation[]>;
    fetchLiquidations(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Liquidation[]>;
    fetchMyTradesWs(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    watchMyTrades(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Trade[]>;
    fetchGreeks(symbol: string, params?: {}): Promise<Greeks>;
    fetchOptionChain(code: string, params?: {}): Promise<OptionChain>;
    fetchOption(symbol: string, params?: {}): Promise<Option>;
    fetchConvertQuote(fromCode: string, toCode: string, amount?: Num, params?: {}): Promise<Conversion>;
    fetchDepositsWithdrawals(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<Transaction[]>;
    fetchDeposits(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Transaction[]>;
    fetchWithdrawals(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<Transaction[]>;
    fetchDepositsWs(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<{}>;
    fetchWithdrawalsWs(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<{}>;
    fetchFundingRateHistory(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<FundingRateHistory[]>;
    fetchFundingHistory(symbol?: Str, since?: Int, limit?: Int, params?: {}): Promise<FundingHistory[]>;
    closePosition(symbol: string, side?: OrderSide, params?: {}): Promise<Order>;
    closeAllPositions(params?: {}): Promise<Position[]>;
    fetchL3OrderBook(symbol: string, limit?: Int, params?: {}): Promise<OrderBook>;
    parseLastPrice(price: any, market?: Market): LastPrice;
    fetchDepositAddress(code: string, params?: {}): Promise<DepositAddress>;
    account(): BalanceAccount;
    commonCurrencyCode(code: string): string;
    currency(code: string): any;
    market(symbol: string): MarketInterface;
    createExpiredOptionMarket(symbol: string): MarketInterface;
    handleWithdrawTagAndParams(tag: any, params: any): any;
    createLimitOrder(symbol: string, side: OrderSide, amount: number, price: number, params?: {}): Promise<Order>;
    createLimitOrderWs(symbol: string, side: OrderSide, amount: number, price: number, params?: {}): Promise<Order>;
    createMarketOrder(symbol: string, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createMarketOrderWs(symbol: string, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createLimitBuyOrder(symbol: string, amount: number, price: number, params?: {}): Promise<Order>;
    createLimitBuyOrderWs(symbol: string, amount: number, price: number, params?: {}): Promise<Order>;
    createLimitSellOrder(symbol: string, amount: number, price: number, params?: {}): Promise<Order>;
    createLimitSellOrderWs(symbol: string, amount: number, price: number, params?: {}): Promise<Order>;
    createMarketBuyOrder(symbol: string, amount: number, params?: {}): Promise<Order>;
    createMarketBuyOrderWs(symbol: string, amount: number, params?: {}): Promise<Order>;
    createMarketSellOrder(symbol: string, amount: number, params?: {}): Promise<Order>;
    createMarketSellOrderWs(symbol: string, amount: number, params?: {}): Promise<Order>;
    costToPrecision(symbol: string, cost: any): string;
    priceToPrecision(symbol: string, price: any): string;
    amountToPrecision(symbol: string, amount: any): string;
    feeToPrecision(symbol: string, fee: any): string;
    currencyToPrecision(code: string, fee: any, networkCode?: any): string;
    forceString(value: any): string;
    isTickPrecision(): boolean;
    isDecimalPrecision(): boolean;
    isSignificantPrecision(): boolean;
    safeNumber(obj: any, key: IndexType, defaultNumber?: Num): Num;
    safeNumberN(obj: object, arr: IndexType[], defaultNumber?: Num): Num;
    parsePrecision(precision?: string): string;
    integerPrecisionToAmount(precision: Str): string;
    loadTimeDifference(params?: {}): Promise<any>;
    implodeHostname(url: string): string;
    fetchMarketLeverageTiers(symbol: string, params?: {}): Promise<LeverageTier[]>;
    createPostOnlyOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createPostOnlyOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createReduceOnlyOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createReduceOnlyOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, params?: {}): Promise<Order>;
    createStopOrder(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, triggerPrice?: Num, params?: {}): Promise<Order>;
    createStopOrderWs(symbol: string, type: OrderType, side: OrderSide, amount: number, price?: Num, triggerPrice?: Num, params?: {}): Promise<Order>;
    createStopLimitOrder(symbol: string, side: OrderSide, amount: number, price: number, triggerPrice: number, params?: {}): Promise<Order>;
    createStopLimitOrderWs(symbol: string, side: OrderSide, amount: number, price: number, triggerPrice: number, params?: {}): Promise<Order>;
    createStopMarketOrder(symbol: string, side: OrderSide, amount: number, triggerPrice: number, params?: {}): Promise<Order>;
    createStopMarketOrderWs(symbol: string, side: OrderSide, amount: number, triggerPrice: number, params?: {}): Promise<Order>;
    safeCurrencyCode(currencyId: Str, currency?: Currency): string;
    filterBySymbolSinceLimit(array: any, symbol?: Str, since?: Int, limit?: Int, tail?: boolean): any;
    filterByCurrencySinceLimit(array: any, code?: any, since?: Int, limit?: Int, tail?: boolean): any;
    filterBySymbolsSinceLimit(array: any, symbols?: string[], since?: Int, limit?: Int, tail?: boolean): any;
    parseLastPrices(pricesData: any, symbols?: string[], params?: {}): LastPrices;
    parseTickers(tickers: any, symbols?: Strings, params?: {}): Tickers;
    parseDepositAddresses(addresses: any, codes?: Strings, indexed?: boolean, params?: {}): DepositAddress[];
    parseBorrowInterests(response: any, market?: Market): BorrowInterest[];
    parseBorrowRate(info: any, currency?: Currency): Dict;
    parseBorrowRateHistory(response: any, code: Str, since: Int, limit: Int): any;
    parseIsolatedBorrowRates(info: any): IsolatedBorrowRates;
    parseFundingRateHistories(response: any, market?: any, since?: Int, limit?: Int): FundingRateHistory[];
    safeSymbol(marketId: Str, market?: Market, delimiter?: Str, marketType?: Str): string;
    parseFundingRate(contract: string, market?: Market): FundingRate;
    parseFundingRates(response: any, symbols?: Strings): FundingRates;
    parseLongShortRatio(info: Dict, market?: Market): LongShortRatio;
    parseLongShortRatioHistory(response: any, market?: any, since?: Int, limit?: Int): LongShortRatio[];
    handleTriggerDirectionAndParams(params: any, exchangeSpecificKey?: Str, allowEmpty?: Bool): any[];
    handleTriggerAndParams(params: any): any[];
    isTriggerOrder(params: any): any[];
    isPostOnly(isMarketOrder: boolean, exchangeSpecificParam: any, params?: {}): boolean;
    handlePostOnly(isMarketOrder: boolean, exchangeSpecificPostOnlyOption: boolean, params?: any): any[];
    fetchLastPrices(symbols?: Strings, params?: {}): Promise<LastPrices>;
    fetchTradingFees(params?: {}): Promise<TradingFees>;
    fetchTradingFeesWs(params?: {}): Promise<TradingFees>;
    fetchTradingFee(symbol: string, params?: {}): Promise<TradingFeeInterface>;
    fetchConvertCurrencies(params?: {}): Promise<Currencies>;
    parseOpenInterest(interest: any, market?: Market): OpenInterest;
    parseOpenInterests(response: any, symbols?: Strings): OpenInterests;
    parseOpenInterestsHistory(response: any, market?: any, since?: Int, limit?: Int): OpenInterest[];
    fetchFundingRate(symbol: string, params?: {}): Promise<FundingRate>;
    fetchFundingInterval(symbol: string, params?: {}): Promise<FundingRate>;
    fetchMarkOHLCV(symbol: any, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OHLCV[]>;
    fetchIndexOHLCV(symbol: string, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OHLCV[]>;
    fetchPremiumIndexOHLCV(symbol: string, timeframe?: string, since?: Int, limit?: Int, params?: {}): Promise<OHLCV[]>;
    handleTimeInForce(params?: {}): string;
    convertTypeToAccount(account: any): any;
    checkRequiredArgument(methodName: string, argument: any, argumentName: any, options?: any[]): void;
    checkRequiredMarginArgument(methodName: string, symbol: Str, marginMode: string): void;
    parseDepositWithdrawFees(response: any, codes?: Strings, currencyIdKey?: any): any;
    parseDepositWithdrawFee(fee: any, currency?: Currency): any;
    depositWithdrawFee(info: any): any;
    assignDefaultDepositWithdrawFees(fee: any, currency?: any): any;
    parseIncome(info: any, market?: Market): object;
    parseIncomes(incomes: any, market?: any, since?: Int, limit?: Int): FundingHistory[];
    getMarketFromSymbols(symbols?: Strings): MarketInterface;
    parseWsOHLCVs(ohlcvs: object[], market?: any, timeframe?: string, since?: Int, limit?: Int): any[];
    fetchTransactions(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<Transaction[]>;
    filterByArrayPositions(objects: any, key: IndexType, values?: any, indexed?: boolean): Position[];
    filterByArrayTickers(objects: any, key: IndexType, values?: any, indexed?: boolean): Dictionary<Ticker>;
    createOHLCVObject(symbol: string, timeframe: string, data: any): Dictionary<Dictionary<OHLCV[]>>;
    handleMaxEntriesPerRequestAndParams(method: string, maxEntriesPerRequest?: Int, params?: {}): [Int, any];
    fetchPaginatedCallDynamic(method: string, symbol?: Str, since?: Int, limit?: Int, params?: {}, maxEntriesPerRequest?: Int, removeRepeated?: boolean): Promise<any>;
    safeDeterministicCall(method: string, symbol?: Str, since?: Int, limit?: Int, timeframe?: Str, params?: {}): Promise<any>;
    fetchPaginatedCallDeterministic(method: string, symbol?: Str, since?: Int, limit?: Int, timeframe?: Str, params?: {}, maxEntriesPerRequest?: any): Promise<any>;
    fetchPaginatedCallCursor(method: string, symbol?: Str, since?: any, limit?: any, params?: {}, cursorReceived?: any, cursorSent?: any, cursorIncrement?: any, maxEntriesPerRequest?: any): Promise<any>;
    fetchPaginatedCallIncremental(method: string, symbol?: Str, since?: any, limit?: any, params?: {}, pageKey?: any, maxEntriesPerRequest?: any): Promise<any>;
    sortCursorPaginatedResult(result: any): any;
    removeRepeatedElementsFromArray(input: any): any;
    handleUntilOption(key: string, request: any, params: any, multiplier?: number): any[];
    safeOpenInterest(interest: Dict, market?: Market): OpenInterest;
    parseLiquidation(liquidation: any, market?: Market): Liquidation;
    parseLiquidations(liquidations: Dict[], market?: Market, since?: Int, limit?: Int): Liquidation[];
    parseGreeks(greeks: Dict, market?: Market): Greeks;
    parseOption(chain: Dict, currency?: Currency, market?: Market): Option;
    parseOptionChain(response: object[], currencyKey?: Str, symbolKey?: Str): OptionChain;
    parseMarginModes(response: object[], symbols?: string[], symbolKey?: Str, marketType?: MarketType): MarginModes;
    parseMarginMode(marginMode: Dict, market?: Market): MarginMode;
    parseLeverages(response: object[], symbols?: string[], symbolKey?: Str, marketType?: MarketType): Leverages;
    parseLeverage(leverage: Dict, market?: Market): Leverage;
    parseConversions(conversions: any[], code?: Str, fromCurrencyKey?: Str, toCurrencyKey?: Str, since?: Int, limit?: Int, params?: {}): Conversion[];
    parseConversion(conversion: Dict, fromCurrency?: Currency, toCurrency?: Currency): Conversion;
    convertExpireDate(date: string): string;
    convertExpireDateToMarketIdDate(date: string): string;
    convertMarketIdExpireDate(date: string): string;
    fetchPositionHistory(symbol: string, since?: Int, limit?: Int, params?: {}): Promise<Position[]>;
    fetchPositionsHistory(symbols?: Strings, since?: Int, limit?: Int, params?: {}): Promise<Position[]>;
    parseMarginModification(data: Dict, market?: Market): MarginModification;
    parseMarginModifications(response: object[], symbols?: Strings, symbolKey?: Str, marketType?: MarketType): MarginModification[];
    fetchTransfer(id: string, code?: Str, params?: {}): Promise<TransferEntry>;
    fetchTransfers(code?: Str, since?: Int, limit?: Int, params?: {}): Promise<TransferEntry[]>;
    cleanUnsubscription(client: any, subHash: string, unsubHash: string): void;
    cleanCache(subscription: Dict): void;
}
export { Exchange, };
