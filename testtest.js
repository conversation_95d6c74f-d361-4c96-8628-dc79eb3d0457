const ccxt = require('ccxt');

async function checkFundingRatesSupport() {
    console.log('Биржи с поддержкой метода fetchFundingRates:\n');

    // Получаем список всех поддерживаемых бирж
    const exchanges = ccxt.exchanges;

    for (const exchangeId of exchanges) {
        try {
            // Создаем экземпляр биржи
            const ExchangeClass = ccxt[exchangeId];
            const exchange = new ExchangeClass();

            // Проверяем поддержку метода fetchFundingRates
            const supportsFundingRates = exchange.has['fetchFundingRates'] || false;

            // Выводим только биржи с поддержкой fetchFundingRates
            if (supportsFundingRates) {
                console.log(exchangeId);
            }

        } catch (error) {
            // Пропускаем биржи с ошибками создания экземпляра
        }
    }
}

// Запускаем проверку
checkFundingRatesSupport().catch(console.error);
