const { GateAPI } = require('./gate_lib');

async function main() {
    
    const gate = new GateAPI();

    try {
        //метод getAllFundings()
        const fundings = await gate.getAllFundings();
        console.log(`Получено фандинг рейтов: ${fundings.length}`);
        console.log(fundings);

        //метод getAllFuturesPrices()
        const futuresPrices = await gate.getAllFuturesPrices();
        console.log(`Получено цен фьючерсов: ${futuresPrices.length}`);
        console.log(futuresPrices);


        //Тестируем метод getCombinedData()
        const combinedData = await gate.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);

    } catch (error) {
        console.error('Ошибка при получении данных с Gate:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
