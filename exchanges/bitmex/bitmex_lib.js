const ccxt = require('ccxt');

class BitmexAPI {
    constructor() {
        this.bitmex = new ccxt.bitmex({
            enableRateLimit: true,
        });
    }

    // Получает данные о фандинге для всех токенов
    async getFundings() {
        let bitmexInfo = [];

        try {
            const fundingRates = await this.bitmex.fetchFundingRates();
            // console.log('Получено фандингов:', Object.keys(fundingRates).length);

            bitmexInfo = Object.entries(fundingRates).map(([symbol, data]) => ({
                exchange: 'bitmex',
                token: symbol,
                fundingRate: data.fundingRate * 100,
                nextFundingTime: new Date(data.fundingTimestamp).getTime()
            }));
        } catch (error) {
            console.error('Ошибка при получении фандинга с биржи BitMex:', error);
        }

        return bitmexInfo;
    }

    // Получает цены фьючерсов
    async getFuturesPrices() {
        let pricesInfo = [];

        try {
            const tickers = await this.bitmex.fetchTickers();
            console.log('Получено тикеров:', Object.keys(tickers).length);

            pricesInfo = Object.entries(tickers).map(([symbol, ticker]) => ({
                exchange: 'bitmex',
                token: symbol,
                lastPrice: ticker.last,
                dailyChange: ticker.change,
                dailyChangePercent: ticker.percentage,
                volume: ticker.volume,
                high: ticker.high,
                low: ticker.low
            }));

        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов:', error);
        }

        return pricesInfo;
    }

    // Получает объединенные данные о фандинге и ценах фьючерсов
    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getFundings(),
                this.getFuturesPrices()
            ]);

            console.log(`Получено данных о фандингах: ${fundings.length}`);
            console.log(`Получено данных о ценах: ${prices.length}`);

            // Создаем Map с данными о фандингах по токенам
            const tokensMap = new Map();
            fundings.forEach(item => {
                tokensMap.set(item.token, {
                    exchange: 'bitmex',
                    token: item.token,
                    fundingRate: item.fundingRate,
                    nextFundingTime: item.nextFundingTime
                });
            });

            // Выводим первые несколько ключей для отладки
            const tokensKeys = Array.from(tokensMap.keys()).slice(0, 5);
            console.log('Примеры ключей в tokensMap:', tokensKeys);

            // Список первых нескольких токенов из цен для сравнения
            const priceTokensExample = prices.slice(0, 5).map(p => p.token);

            // Объединяем данные
            const combinedData = [];
            let matchCount = 0;

            // Для каждого тикера цены находим соответствующий фандинг
            prices.forEach(price => {
                const tokenData = tokensMap.get(price.token);

                if (tokenData) {
                    matchCount++;
                    // Если нашли соответствие, объединяем данные
                    combinedData.push({
                        ...tokenData,
                        lastPrice: price.lastPrice,
                        dailyChange: price.dailyChange,
                        dailyChangePercent: price.dailyChangePercent,
                        volume: price.volume,
                        high: price.high,
                        low: price.low
                    });
                } else {
                    // Если соответствия не нашли, добавляем только данные о цене
                    combinedData.push({
                        exchange: 'bitmex',
                        token: price.token,
                        lastPrice: price.lastPrice,
                        dailyChange: price.dailyChange,
                        dailyChangePercent: price.dailyChangePercent,
                        volume: price.volume,
                        high: price.high,
                        low: price.low
                    });
                }
            });

            console.log(`Найдено совпадений по ключам: ${matchCount} из ${prices.length}`);
            console.log(`Получены объединенные данные по ${combinedData.length} токенам с Bitmex`);
            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных:', error);
            return [];
        }
    }
}

module.exports = BitmexAPI;
